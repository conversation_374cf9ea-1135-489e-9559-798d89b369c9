/**
 * 测试数据适配器功能
 * 用于验证重构后的数据适配逻辑是否正常工作
 */

import { adaptProjectDataFromStore } from './dataAdapter.js';

// 模拟pinia store中的项目数据
const mockProjectData = {
  project_info: {
    id: 1,
    name: "测试项目",
    description: "用于测试的项目数据",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  operating_params: {
    pv_capacity_mw: 500,
    pv_utilization_hours: 1650,
    pv_degradation_y1_percent: 2.0,
    pv_degradation_ongoing_percent: 0.55,
    pv_system_efficiency_percent: 82.3,
    pv_transmission_distance_km: 60,
    pv_equipment_depreciation_years: 25,
    pv_grid_price_tax_included_yuan_per_kwh: 0.2829,
    wind_capacity_mw: 200,
    wind_utilization_hours: 3000,
    wind_system_efficiency_percent: 82.3,
    wind_transmission_distance_km: 60,
    wind_equipment_depreciation_years: 25,
    h2_plant_capacity_mw: 100,
    h2_consumption_kwh_per_nm3: 4.9,
    h2_base_consumption_kwh_per_kg: 54.88,
    h2_equipment_service_years: 25,
    h2_consumption_increase_annual: 0.01,
    h2_water_price_yuan_per_ton: 8.6,
    h2_wastewater_price_yuan_per_ton: 40,
    h2_staff_count: 48,
    h2_staff_salary_wan_yuan_per_year: 10,
    h2_price_transport: 20,
    h2_price_chemical: 16.8,
    h2_storage_investment_wan_yuan: 0,
    h2_storage_capacity_tons: 700,
    h2_equipment_depreciation_years: 25,
    o2_price_per_ton: 200,
    loan_term_years: 25,
    loan_interest_rate: 0.05,
    finance_land_rent_wan_yuan_per_year: 10,
    loan_ratio: 0.8,
    finance_loan_total_wan_yuan: 0,
    ammonia_price_yuan_per_ton: 3500,
    ammonia_consumption_tons_per_hour: 20,
    electrochemical_energy_storage_scale_mw: 25
  },
  fixed_investments: [
    {
      id: 1,
      project_id: 1,
      item_name: "光伏电站",
      scale: 500,
      scale_unit: "MW",
      unit_cost: 4000,
      unit_cost_unit: "万元/MW",
      offset: 0,
      total_investment: 200000,
      is_total_row: false,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 2,
      project_id: 1,
      item_name: "风电站",
      scale: 200,
      scale_unit: "MW",
      unit_cost: 5500,
      unit_cost_unit: "万元/MW",
      offset: 0,
      total_investment: 110000,
      is_total_row: false,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    }
  ],
  energy_material_balance: [
    {
      id: 1,
      project_id: 1,
      year: 1,
      pv_generation_mwh: 825000,
      wind_generation_mwh: 600000,
      total_generation_mwh: 1425000,
      grid_sales_mwh: 500000,
      h2_electricity_mwh: 925000,
      h2_production_tons: 16000,
      h2_sales_transport_tons: 8000,
      h2_sales_chemical_tons: 8000,
      o2_production_tons: 128000,
      calculated_at: "2024-01-01T00:00:00Z",
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    }
  ],
  cash_flow: [
    {
      id: 1,
      project_id: 1,
      year: -2,
      h2_transport_sales_income: 0,
      h2_chemical_sales_income: 0,
      o2_sales_income: 0,
      total_cash_inflow: 0,
      pv_station_investment: 100000,
      wind_station_investment: 55000,
      total_cash_outflow: 155000,
      net_cash_flow_before_tax: -155000,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 2,
      project_id: 1,
      year: 1,
      h2_transport_sales_income: 160000,
      h2_chemical_sales_income: 134400,
      o2_sales_income: 25600,
      total_cash_inflow: 320000,
      pv_station_investment: 0,
      wind_station_investment: 0,
      h2_raw_materials_cost: 50000,
      h2_maintenance_cost: 20000,
      total_cash_outflow: 70000,
      net_cash_flow_before_tax: 250000,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    }
  ]
};

/**
 * 测试数据适配器功能
 */
export function testDataAdapter() {
  console.log('开始测试数据适配器...');
  
  try {
    const adaptedData = adaptProjectDataFromStore(mockProjectData);
    
    console.log('数据适配成功!');
    console.log('能源数据模块数量:', Object.keys(adaptedData.energyData).length);
    console.log('产品方案数据条数:', adaptedData.solutionData.length);
    console.log('现金流数据类型:', Object.keys(adaptedData.CashFlowData));
    
    // 详细检查能源数据
    console.log('光伏参数条数:', adaptedData.energyData.photovoltaicData?.length || 0);
    console.log('风电参数条数:', adaptedData.energyData.windPowerData?.length || 0);
    console.log('制氢厂参数条数:', adaptedData.energyData.hydrogenPlantData?.length || 0);
    
    // 详细检查现金流数据
    console.log('现金流入条数:', adaptedData.CashFlowData.cashInflow?.length || 0);
    console.log('现金流出条数:', adaptedData.CashFlowData.cashOutflow?.length || 0);
    console.log('税务计算条数:', adaptedData.CashFlowData.NetCashFlow?.length || 0);
    
    return adaptedData;
  } catch (error) {
    console.error('数据适配测试失败:', error);
    return null;
  }
}

/**
 * 在浏览器控制台中运行测试
 */
if (typeof window !== 'undefined') {
  window.testDataAdapter = testDataAdapter;
  console.log('测试函数已添加到window对象，可以在控制台运行: testDataAdapter()');
}
