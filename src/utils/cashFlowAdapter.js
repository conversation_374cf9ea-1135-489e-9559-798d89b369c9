/**
 * 现金流数据适配器
 * 将 Pinia store 中的 currentProject.cash_flow 数据转换为表格所需的格式
 */

/**
 * 格式化数字为字符串，保留2位小数
 * @param {number} value - 数值
 * @returns {string} 格式化后的字符串
 */
function formatNumber(value) {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00';
  }
  return Number(value).toFixed(2);
}

/**
 * 现金流入项目映射
 */
const CASH_INFLOW_MAPPING = {
  h2_transport_sales_income: '销售氢气（交通）',
  h2_chemical_sales_income: '销售氢气（化工）',
  o2_sales_income: '销售氧气',
  ammonia_sales_income: '销售液氨',
  steam_sales_income: '销售蒸汽',
  pv_electricity_sales_income: '光伏售电收入',
  wind_electricity_sales_income: '风电售电收入',
  other_income: '其他收入',
  pv_station_residual_value: '光伏电站残值',
  wind_station_residual_value: '风电站残值',
  pv_transmission_residual_value: '光伏送出线路残值',
  wind_transmission_residual_value: '风电送出线路残值',
  step_up_station_residual_value: '升压站残值',
  step_down_station_residual_value: '降压站残值',
  battery_storage_residual_value: '电池储能系统残值',
  h2_plant_residual_value: '制氢厂残值',
  h2_storage_residual_value: '储氢系统残值',
  h2_auxiliary_residual_value: '制氢公辅设施残值',
  synthesis_equipment_residual_value: '合成设备残值',
  other_equipment_residual_value: '其他设备残值',
  government_subsidy: '政府直接补贴'
};

/**
 * 现金流出项目映射
 */
const CASH_OUTFLOW_MAPPING = {
  pv_station_investment: '光伏电站投资',
  wind_station_investment: '风电站投资',
  pv_transmission_investment: '光伏送出线路投资',
  wind_transmission_investment: '风电送出线路投资',
  step_up_station_investment: '升压站投资',
  step_down_station_investment: '降压站投资',
  battery_storage_investment: '电池储能系统',
  h2_plant_investment: '制氢厂投资',
  h2_storage_investment: '储氢系统投资',
  h2_auxiliary_investment: '制氢公辅设施投资',
  synthesis_equipment_investment: '合成设备投资',
  other_equipment_investment: '其他设备投资',
  h2_raw_materials_cost: '制氢站外购原材料',
  h2_maintenance_cost: '制氢站维修费用',
  h2_personnel_cost: '制氢人员支出',
  h2_other_operating_cost: '制氢站其他运营管理支出',
  h2_water_cost: '制氢用水成本',
  h2_wastewater_cost: '制氢排污水费',
  h2_insurance_cost: '制氢保险成本',
  pv_operating_cost: '光伏运营、管理成本',
  pv_insurance_cost: '光伏保险成本',
  pv_financial_cost: '光伏财务成本',
  wind_operating_cost: '风电运营、管理成本',
  wind_insurance_cost: '风电保险成本',
  wind_financial_cost: '风电财务成本'
};

/**
 * 税务相关项目映射
 */
const TAX_MAPPING = {
  net_cash_flow_before_tax: '所得税前净现金流',
  cumulative_net_cash_flow_before_tax: '所得税前累计净现金流',
  depreciation: '折旧',
  vat_output: '增值税销项',
  vat_input_operating: '增值税进项（一）',
  vat_input_fixed_assets: '增值税进项（二）',
  vat_payable: '缴纳增值税',
  vat_surcharge: '缴纳增值税附加',
  total_vat_and_surcharge: '增值税及附加总额',
  income_tax: '所得税',
  net_cash_flow_after_tax: '税后净现金流',
  cumulative_net_cash_flow_after_tax: '累计税后净现金流'
};

/**
 * 构建表格行数据
 * @param {string} itemName - 项目名称
 * @param {Array} cashFlowArray - 现金流数据数组
 * @param {string} fieldKey - 字段键名
 * @returns {Object} 表格行数据
 */
function buildTableRow(itemName, cashFlowArray, fieldKey) {
  const row = {
    '项目名称': itemName,
    '合计': '0.00',
    '-2': '0.00',
    '-1': '0.00'
  };

  // 处理每年的数据
  cashFlowArray.forEach(item => {
    const value = item[fieldKey] || 0;
    const year = item.year;

    if (year !== undefined && year !== null) {
      row[year.toString()] = formatNumber(value);
    }
  });

  // 设置合计值 - 直接使用 year=0 的数据作为合计
  const totalItem = cashFlowArray.find(item => item.year === 0);
  if (totalItem && totalItem[fieldKey] !== undefined) {
    row['合计'] = formatNumber(totalItem[fieldKey]);
  } else {
    // 如果没有 year=0 的数据，则手动计算合计（排除建设期）
    console.log('缺失 year=0 的数据，手动计算合计');
    let total = 0;
    cashFlowArray.forEach(item => {
      if (item.year > 0) { // 只计算运营期数据
        total += Number(item[fieldKey] || 0);
      }
    });
    row['合计'] = formatNumber(total);
  }

  return row;
}

/**
 * 计算合计行数据
 * @param {Array} dataRows - 数据行数组
 * @param {string} totalLabel - 合计行标签
 * @param {Array} cashFlowData - 原始现金流数据，用于精确计算
 * @param {Array} fieldKeys - 参与计算的字段键数组
 * @returns {Object} 合计行数据
 */
function calculateTotalRow(dataRows, totalLabel, cashFlowData = null, fieldKeys = null) {
  if (dataRows.length === 0) return null;

  const totalRow = {
    '项目名称': totalLabel,
    '合计': '0.00',
    '-2': '0.00',
    '-1': '0.00',
    isTotal: true // 标记为合计行
  };

  // 获取所有年份列
  const yearColumns = new Set();
  dataRows.forEach(row => {
    Object.keys(row).forEach(key => {
      if (/^-?\d+$/.test(key)) {
        yearColumns.add(key);
      }
    });
  });

  if (cashFlowData && fieldKeys) {
    // 使用原始数据进行精确计算
    yearColumns.forEach(yearKey => {
      const year = parseInt(yearKey);
      let total = 0;
      
      // 找到对应年份的数据
      const yearData = cashFlowData.find(item => item.year === year);
      if (yearData) {
        fieldKeys.forEach(fieldKey => {
          total += Number(yearData[fieldKey] || 0);
        });
      }
      
      totalRow[yearKey] = formatNumber(total);
    });

    // 计算总合计 - 使用原始数据
    let grandTotal = 0;
    // 找到 year=0 的总计数据，如果没有则手动计算运营期合计
    const totalItem = cashFlowData.find(item => item.year === 0);
    if (totalItem) {
      fieldKeys.forEach(fieldKey => {
        grandTotal += Number(totalItem[fieldKey] || 0);
      });
    } else {
      // 如果没有 year=0 的数据，则计算运营期数据总和
      yearColumns.forEach(yearKey => {
        const year = parseInt(yearKey);
        if (year > 0) { // 只计算运营期数据
          const yearData = cashFlowData.find(item => item.year === year);
          if (yearData) {
            fieldKeys.forEach(fieldKey => {
              grandTotal += Number(yearData[fieldKey] || 0);
            });
          }
        }
      });
    }
    totalRow['合计'] = formatNumber(grandTotal);
  } else {
    // 兜底方案：使用格式化后的数据计算（保持原有逻辑）
    yearColumns.forEach(yearKey => {
      let total = 0;
      dataRows.forEach(row => {
        const value = parseFloat(row[yearKey]) || 0;
        total += value;
      });
      totalRow[yearKey] = formatNumber(total);
    });

    // 计算总合计
    let grandTotal = 0;
    yearColumns.forEach(yearKey => {
      const value = parseFloat(totalRow[yearKey]) || 0;
      grandTotal += value;
    });
    totalRow['合计'] = formatNumber(grandTotal);
  }

  return totalRow;
}

/**
 * 转换现金流数据为表格格式
 * @param {Array} cashFlowData - 现金流数据数组
 * @returns {Object} 包含四种类型表格数据的对象
 */
export function adaptCashFlowData(cashFlowData) {
  if (!Array.isArray(cashFlowData) || cashFlowData.length === 0) {
    return {
      cashInflow: [],
      cashOutflow: [],
      NetCashFlow: [],
      all: []
    };
  }

  // 现金流入数据
  const cashInflow = [];
  Object.entries(CASH_INFLOW_MAPPING).forEach(([fieldKey, itemName]) => {
    const row = buildTableRow(itemName, cashFlowData, fieldKey);
    cashInflow.push(row);
  });

  // 添加现金流入合计行
  if (cashInflow.length > 0) {
    const inflowFieldKeys = Object.keys(CASH_INFLOW_MAPPING);
    const inflowTotal = calculateTotalRow(cashInflow, '现金流入合计', cashFlowData, inflowFieldKeys);
    if (inflowTotal) {
      cashInflow.push(inflowTotal);
    }
  }

  // 现金流出数据
  const cashOutflow = [];
  Object.entries(CASH_OUTFLOW_MAPPING).forEach(([fieldKey, itemName]) => {
    const row = buildTableRow(itemName, cashFlowData, fieldKey);
    cashOutflow.push(row);
  });

  // 添加现金流出合计行
  if (cashOutflow.length > 0) {
    const outflowFieldKeys = Object.keys(CASH_OUTFLOW_MAPPING);
    const outflowTotal = calculateTotalRow(cashOutflow, '现金流出合计', cashFlowData, outflowFieldKeys);
    if (outflowTotal) {
      cashOutflow.push(outflowTotal);
    }
  }

  // 税务数据
  const NetCashFlow = [];
  Object.entries(TAX_MAPPING).forEach(([fieldKey, itemName]) => {
    const row = buildTableRow(itemName, cashFlowData, fieldKey);
    NetCashFlow.push(row);
  });

  // 全部数据 - 合并所有类型的数据
  const all = [
    ...cashInflow,
    { '项目名称': '', isSpacer: true }, // 分隔行
    ...cashOutflow,
    { '项目名称': '', isSpacer: true }, // 分隔行
    ...NetCashFlow
  ];

  return {
    cashInflow,
    cashOutflow,
    NetCashFlow,
    all
  };
}

/**
 * 获取年份列表（用于动态表格列）
 * @param {Array} cashFlowData - 现金流数据数组
 * @returns {Array} 年份数组
 */
export function getYearColumns(cashFlowData) {
  if (!Array.isArray(cashFlowData) || cashFlowData.length === 0) {
    return [];
  }

  const years = new Set();
  cashFlowData.forEach(item => {
    if (item.year !== undefined && item.year !== null && item.year > 0) {
      years.add(item.year);
    }
  });

  return Array.from(years).sort((a, b) => a - b);
}

/**
 * 检查是否有建设期数据
 * @param {Array} cashFlowData - 现金流数据数组
 * @returns {boolean} 是否有建设期数据
 */
export function hasConstructionPeriod(cashFlowData) {
  if (!Array.isArray(cashFlowData)) return false;
  
  return cashFlowData.some(item => 
    item.is_construction_period === true || item.year < 0
  );
}
