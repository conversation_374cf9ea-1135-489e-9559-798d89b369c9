/**
 * 结果分析页面数据适配器
 * 将 projectStore 中的数据转换为页面所需的格式
 */

/**
 * 计算KPI指标
 * @param {Object} project - 项目数据
 * @returns {Object} KPI指标对象
 */
export function calculateKPIs(project) {
  if (!project || !project.project_info || !project.operating_params || !project.cash_flow) {
    return {
      totalInvestment: 0,
      totalCapacity: 0,
      averageIRR: 0,
      totalNPV: 0
    };
  }

  const { project_info, operating_params, cash_flow } = project;
  
  // 从year=0（合计项）获取总投资
  const totalInvestmentData = cash_flow.find(item => item.year === 0);
  const totalInvestment = totalInvestmentData ? totalInvestmentData.total_cash_outflow : 0;
  
  // 计算总装机容量（光伏 + 风电 + 制氢 + 储能）
  const totalCapacity = (
    (operating_params.pv_capacity_mw || 0) +
    (operating_params.wind_capacity_mw || 0) +
    (operating_params.h2_plant_capacity_mw || 0) +
    (operating_params.electrochemical_energy_storage_scale_mw || 0)
  );

  return {
    totalInvestment: Math.round(totalInvestment), // 万元
    totalCapacity: Math.round(totalCapacity * 100) / 100, // MW，保留2位小数
    averageIRR: Math.round(project_info.irr_before_tax * 100) / 100, // %，保留2位小数
    totalNPV: Math.round(project_info.npv_before_tax) // 万元
  };
}

/**
 * 获取投资构成数据（饼图）
 * @param {Object} project - 项目数据
 * @returns {Array} 投资构成数据数组
 */
export function getInvestmentComposition(project) {
  if (!project || !project.cash_flow) {
    return [];
  }

  // 从year=0（合计项）获取投资数据
  const totalInvestmentData = project.cash_flow.find(item => item.year === 0);
  if (!totalInvestmentData) {
    return [];
  }

  const composition = [];
  
  // 光伏电站投资
  if (totalInvestmentData.pv_station_investment > 0) {
    composition.push({
      value: totalInvestmentData.pv_station_investment,
      name: '光伏电站'
    });
  }
  
  // 风电站投资
  if (totalInvestmentData.wind_station_investment > 0) {
    composition.push({
      value: totalInvestmentData.wind_station_investment,
      name: '风电站'
    });
  }
  
  // 制氢厂投资
  if (totalInvestmentData.h2_plant_investment > 0) {
    composition.push({
      value: totalInvestmentData.h2_plant_investment,
      name: '制氢厂'
    });
  }
  
  // 储能系统投资
  if (totalInvestmentData.battery_storage_investment > 0) {
    composition.push({
      value: totalInvestmentData.battery_storage_investment,
      name: '储能系统'
    });
  }
  
  // 输电系统投资（光伏输电 + 风电输电 + 升压站 + 降压站）
  const transmissionInvestment = (
    (totalInvestmentData.pv_transmission_investment || 0) +
    (totalInvestmentData.wind_transmission_investment || 0) +
    (totalInvestmentData.step_up_station_investment || 0) +
    (totalInvestmentData.step_down_station_investment || 0)
  );
  if (transmissionInvestment > 0) {
    composition.push({
      value: transmissionInvestment,
      name: '输电系统'
    });
  }
  
  // 其他投资（制氢辅助设备 + 合成设备 + 其他设备）
  const otherInvestment = (
    (totalInvestmentData.h2_auxiliary_investment || 0) +
    (totalInvestmentData.synthesis_equipment_investment || 0) +
    (totalInvestmentData.other_equipment_investment || 0)
  );
  if (otherInvestment > 0) {
    composition.push({
      value: otherInvestment,
      name: '其他设备'
    });
  }

  return composition;
}

/**
 * 获取各模块收益率对比数据
 * @param {Object} project - 项目数据
 * @returns {Object} 包含模块名称和收益率的对象
 */
export function getModuleROIComparison(project) {
  if (!project || !project.operating_params) {
    return {
      categories: [],
      data: []
    };
  }

  const { operating_params } = project;
  const categories = [];
  const data = [];

  // 根据装机容量判断是否包含该模块，并使用简化的ROI计算
  // 注：这里使用简化计算，实际项目中可能需要更复杂的模块收益率分析

  if (operating_params.pv_capacity_mw > 0) {
    categories.push('光伏发电');
    // 简化计算：基于上网电价和利用小时数
    const pvROI = (operating_params.pv_grid_price_tax_included_yuan_per_kwh * 
                   operating_params.pv_utilization_hours * 0.01); // 简化的ROI估算
    data.push(Math.round(pvROI * 100) / 100);
  }

  if (operating_params.wind_capacity_mw > 0) {
    categories.push('风力发电');
    const windROI = (operating_params.grid_price_tax_included_yuan_per_kwh * 
                     operating_params.wind_utilization_hours * 0.01);
    data.push(Math.round(windROI * 100) / 100);
  }

  if (operating_params.h2_plant_capacity_mw > 0) {
    categories.push('制氢系统');
    // 制氢ROI基于氢气价格
    const h2ROI = ((operating_params.h2_price_transport + operating_params.h2_price_chemical) / 2 * 0.1);
    data.push(Math.round(h2ROI * 100) / 100);
  }

  if (operating_params.electrochemical_energy_storage_scale_mw > 0) {
    categories.push('储能系统');
    // 储能ROI简化计算
    const storageROI = 8.5; // 简化固定值
    data.push(storageROI);
  }

  return {
    categories,
    data
  };
}

/**
 * 获取现金流数据
 * @param {Object} project - 项目数据
 * @param {string} type - 'cumulative' | 'annual'
 * @param {string} taxType - 'after_tax' | 'before_tax'
 * @returns {Object} 现金流图表数据
 */
export function getCashflowData(project, type = 'cumulative', taxType = 'after_tax') {
  if (!project || !project.cash_flow) {
    return {
      categories: [],
      series: []
    };
  }

  // 过滤掉year=0的合计项，获取年度数据
  const yearlyData = project.cash_flow.filter(item => item.year > 0).sort((a, b) => a.year - b.year);
  
  if (yearlyData.length === 0) {
    return {
      categories: [],
      series: []
    };
  }

  const categories = yearlyData.map(item => `第${item.year}年`);
  
  // 根据税前/税后和累积/年度选择对应的数据字段
  let fieldName;
  let seriesName;
  
  if (taxType === 'after_tax') {
    if (type === 'cumulative') {
      fieldName = 'cumulative_net_cash_flow_after_tax';
      seriesName = '累积现金流（税后）';
    } else {
      fieldName = 'net_cash_flow_after_tax';
      seriesName = '年度现金流（税后）';
    }
  } else {
    if (type === 'cumulative') {
      fieldName = 'cumulative_net_cash_flow_before_tax';
      seriesName = '累积现金流（税前）';
    } else {
      fieldName = 'net_cash_flow_before_tax';
      seriesName = '年度现金流（税前）';
    }
  }
  
  const series = [{
    name: seriesName,
    type: 'line',
    data: yearlyData.map(item => {
      const value = item[fieldName];
      return Math.round(value);
    }),
    smooth: true,
    itemStyle: {
      color: taxType === 'after_tax' ? '#5470c6' : '#91cc75'
    },
    lineStyle: {
      color: taxType === 'after_tax' ? '#5470c6' : '#91cc75'
    }
  }];

  return {
    categories,
    series
  };
}

/**
 * 格式化数字显示
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数，默认为0
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, decimals = 0) {
  if (typeof num !== 'number' || isNaN(num)) {
    return '0';
  }
  
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num);
}

/**
 * 格式化百分比显示
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercent(num, decimals = 1) {
  if (typeof num !== 'number' || isNaN(num)) {
    return '0%';
  }
  
  return `${num.toFixed(decimals)}%`;
}

/**
 * 获取模块状态标签类型
 * @param {number} value - 数值
 * @param {string} type - 类型 'capacity' | 'roi'
 * @returns {string} Element Plus 标签类型
 */
export function getStatusTagType(value, type = 'capacity') {
  if (type === 'capacity') {
    if (value === 0) return 'info';
    if (value < 50) return 'warning';
    return 'success';
  }
  
  if (type === 'roi') {
    if (value < 5) return 'danger';
    if (value < 10) return 'warning';
    return 'success';
  }
  
  return 'info';
}
