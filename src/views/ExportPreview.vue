<template>
	<div class="export-preview-page">
		<div class="preview-header">
			<div class="header-left">
				<h1>导出报告</h1>
				<!-- 项目信息显示 -->
				<div class="project-info" v-if="currentProjectInfo">
					<el-tag type="primary" size="large">
						<el-icon><folder /></el-icon>
						{{ currentProjectInfo.name }}
					</el-tag>
					<span class="project-time">{{ formatTime(currentProjectInfo.updated_at) }}</span>
				</div>
				<div class="project-info" v-else>
					<el-tag type="warning" size="large">
						<el-icon><warning /></el-icon>
						未选择项目
					</el-tag>
				</div>
			</div>

			<div class="header-actions">
				<el-button @click="refreshPreview" :loading="loading">
					<el-icon><refresh /></el-icon>
					刷新预览
				</el-button>
				<el-button type="primary" @click="downloadExcel" :loading="downloading" :disabled="!hasValidData">
					<el-icon><download /></el-icon>
					下载Excel文件
				</el-button>
				<el-button @click="closeWindow">
					<el-icon><close /></el-icon>
					关闭
				</el-button>
			</div>
		</div>

		<div class="preview-content" v-loading="loading">
			<el-tabs v-model="activeSheet" class="sheet-tabs">
				<el-tab-pane label="能源项目测算" name="energy">
					<div class="sheet-content">
						<el-table 
							:data="energyDataCombined" 
							stripe 
							style="width: 100%" 
							max-height="500"
							:row-class-name="({ row }) => {
								if (row.isHeader) return 'header-row';
								if (row.isSpacer) return 'spacer-row';
								return '';
							}"
						>
							<el-table-column prop="parameter" label="参数名称" width="200" />
							<el-table-column prop="value" label="数值" width="120" />
							<el-table-column prop="unit" label="单位" width="100" />
							<el-table-column prop="category" label="类别" width="120" />
							<el-table-column prop="remark" label="备注" />
						</el-table>
						<div class="data-summary">
							<el-text type="info">共 {{ energyDataCombined.length }} 条数据</el-text>
						</div>
					</div>
				</el-tab-pane>

				<el-tab-pane label="产品方案" name="solution">
					<div class="sheet-content">
						<el-table 
							:data="solutionData" 
							stripe 
							style="width: 100%" 
							max-height="500"
							:row-class-name="({ row }) => {
								if (row.isHeader) return 'header-row';
								if (row.isSpacer) return 'spacer-row';
								return '';
							}"
						>
							<el-table-column prop="parameter" label="方案名称/参数" width="200" />
							<el-table-column prop="value" label="数值" width="120" />
							<el-table-column prop="unit" label="单位" width="100" />
							<el-table-column prop="category" label="类别" width="120" />
							<el-table-column prop="remark" label="备注" />
						</el-table>
						<div class="data-summary">
							<el-text type="info">共 {{ solutionData.length }} 条数据</el-text>
						</div>
					</div>
				</el-tab-pane>

				<el-tab-pane label="现金流量分析" name="cashflow">
					<div class="sheet-content">
						<div class="cashflow-section" v-if="exportCashFlowData.length > 0">
							<h3>现金流量表</h3>
							<div class="table-container">
								<el-table 
									:data="exportCashFlowData" 
									border 
									stripe 
									class="cashflow-table"
									:row-class-name="({ row }) => {
										if (row.isTotal) return 'total-row';
										if (row.isSpacer) return 'spacer-row';
										return '';
									}"
								>
									<el-table-column prop="项目名称" label="项目名称" width="200" fixed="left" align="left" />
									<el-table-column prop="合计" label="合计" width="150" align="right" />
									
									<el-table-column 
										v-if="hasCashFlowConstructionPeriod"
										label="建设期"
										align="center"
									>
										<el-table-column prop="-2" label="-2" width="120" align="right" />
										<el-table-column prop="-1" label="-1" width="120" align="right" />
									</el-table-column>
									
									<el-table-column label="运营期" align="center">
										<el-table-column 
											v-for="year in cashFlowYearColumns"
											:key="`year-${year}`"
											:prop="year.toString()"
											:label="year.toString()"
											width="120"
											align="right"
										/>
									</el-table-column>
								</el-table>
							</div>
						</div>

						<div class="data-summary" v-if="exportCashFlowData.length > 0">
							<el-text type="info">
								现金流数据：共 {{ exportCashFlowData.filter(item => !item.isSpacer).length }} 条
							</el-text>
						</div>
						
						<div v-else class="data-summary">
							<el-text type="warning">暂无现金流数据，请先在现金流页面生成数据</el-text>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Refresh, Download, Close, Folder, Warning } from '@element-plus/icons-vue';
import { useProjectStore } from '../store/modules/project';
import * as XLSX from 'xlsx-js-style';
import { adaptCashFlowData, getYearColumns, hasConstructionPeriod } from '../utils/cashFlowAdapter';

const router = useRouter();
const projectStore = useProjectStore();

const loading = ref(false);
const downloading = ref(false);
const activeSheet = ref('energy');

// 从pinia store获取当前项目数据
const currentProject = computed(() => projectStore.currentProject);

// 获取项目基本信息
const currentProjectInfo = computed(() => {
	if (!currentProject.value || !currentProject.value.project_info) {
		return null;
	}
	return currentProject.value.project_info;
});

// 检查是否有有效数据
const hasValidData = computed(() => {
	if (!currentProject.value) return false;
	
	// 检查项目基础信息
	const hasProjectInfo = currentProject.value.project_info && currentProject.value.project_info.id;
	
	// 检查至少有一种类型的数据
	const hasOperatingParams = currentProject.value.operating_params && 
		Object.keys(currentProject.value.operating_params).length > 0;
	const hasFixedInvestments = currentProject.value.fixed_investments && 
		currentProject.value.fixed_investments.length > 0;
	const hasCashFlow = currentProject.value.cash_flow && 
		currentProject.value.cash_flow.length > 0;
	
	// 至少需要有项目信息和其中一种数据类型
	return hasProjectInfo && (hasOperatingParams || hasFixedInvestments || hasCashFlow);
});

// 格式化时间显示
const formatTime = (timeStr) => {
	if (!timeStr) return '';
	const date = new Date(timeStr);
	return date.toLocaleString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
	});
};

// 刷新预览数据
const refreshPreview = () => {
	if (!currentProject.value) {
		ElMessage.warning('没有可用的项目数据');
		return;
	}
	
	console.log('刷新预览数据，当前项目:', currentProjectInfo.value?.name);
	ElMessage.success('预览数据已刷新');
};

// 关闭窗口
const closeWindow = () => {
	// 如果有历史记录，返回上一页；否则跳转到仪表盘
	if (window.history.length > 1) {
		router.back();
	} else {
		router.push('/dashboard');
	}
};

// 直接从pinia store获取能源项目测算数据
const energyDataCombined = computed(() => {
	if (!currentProject.value?.operating_params) {
		console.warn('缺少 operating_params 数据');
		return [];
	}
	
	const params = currentProject.value.operating_params;
	console.log('能源参数数据:', params);
	const data: Array<{ parameter: string; value: any; unit: string; category: string; remark: string; isHeader?: boolean; isSpacer?: boolean }> = [];
	
	// 1. 光伏系统模块
	data.push(
		{ parameter: '光伏系统', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '光伏装机容量', value: params.pv_capacity_mw || 0, unit: 'MW', category: '光伏系统', remark: '光伏电站装机规模' },
		{ parameter: '光伏利用小时数', value: params.pv_utilization_hours || 0, unit: 'h', category: '光伏系统', remark: '年平均发电小时数' },
		{ parameter: '光伏首年衰减率', value: params.pv_degradation_y1_percent || 0, unit: '%', category: '光伏系统', remark: '首年功率衰减' },
		{ parameter: '光伏后续衰减率', value: params.pv_degradation_ongoing_percent || 0, unit: '%/年', category: '光伏系统', remark: '后续年度功率衰减' },
		{ parameter: '光伏系统效率', value: params.pv_system_efficiency_percent || 0, unit: '%', category: '光伏系统', remark: '系统综合效率' },
		{ parameter: '光伏送电距离', value: params.pv_transmission_distance_km || 0, unit: 'km', category: '光伏系统', remark: '光伏送电距离' },
		{ parameter: '光伏设备折旧年限', value: params.pv_equipment_depreciation_years || 0, unit: '年', category: '光伏系统', remark: '设备折旧年限' },
		{ parameter: '光伏上网电价', value: params.pv_grid_price_tax_included_yuan_per_kwh || 0, unit: '元/kWh', category: '光伏系统', remark: '含税上网电价' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);
	
	// 2. 风电系统模块
	data.push(
		{ parameter: '风电系统', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '风电装机容量', value: params.wind_capacity_mw || 0, unit: 'MW', category: '风电系统', remark: '风电场装机规模' },
		{ parameter: '风电利用小时数', value: params.wind_utilization_hours || 0, unit: 'h', category: '风电系统', remark: '年平均发电小时数' },
		{ parameter: '风电系统效率', value: params.wind_system_efficiency_percent || 0, unit: '%', category: '风电系统', remark: '系统综合效率' },
		{ parameter: '风电送电距离', value: params.wind_transmission_distance_km || 0, unit: 'km', category: '风电系统', remark: '风电送电距离' },
		{ parameter: '风电设备折旧年限', value: params.wind_equipment_depreciation_years || 0, unit: '年', category: '风电系统', remark: '设备折旧年限' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);
	
	// 3. 电网系统模块
	data.push(
		{ parameter: '电网系统', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '电网送电价格', value: params.grid_price_tax_included_yuan_per_kwh || 0, unit: '元/kWh', category: '电网系统', remark: '含税送电价格' },
		{ parameter: '电网输电线路长度', value: params.grid_transmission_length_km || 0, unit: 'km', category: '电网系统', remark: '输电线路长度' },
		{ parameter: '电网输电线路单价', value: params.grid_transmission_unit_cost_wan_yuan_per_km || 0, unit: '万元/km', category: '电网系统', remark: '输电线路单位成本' },
		{ parameter: '升压站容量', value: params.grid_step_up_station_capacity_mva || 0, unit: 'MVA', category: '电网系统', remark: '升压站装机容量' },
		{ parameter: '升压站单价', value: params.grid_step_up_station_unit_cost_wan_yuan_per_mva || 0, unit: '万元/MVA', category: '电网系统', remark: '升压站单位成本' },
		{ parameter: '降压站容量', value: params.grid_step_down_station_capacity_mva || 0, unit: 'MVA', category: '电网系统', remark: '降压站装机容量' },
		{ parameter: '降压站单价', value: params.grid_step_down_station_unit_cost_wan_yuan_per_mva || 0, unit: '万元/MVA', category: '电网系统', remark: '降压站单位成本' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);
	
	// 4. 制氢厂模块
	data.push(
		{ parameter: '制氢厂', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '制氢厂装机容量', value: params.h2_plant_capacity_mw || 0, unit: 'MW', category: '制氢厂', remark: '电解制氢装机规模' },
		{ parameter: '制氢单耗', value: params.h2_consumption_kwh_per_nm3 || 0, unit: 'kWh/Nm³', category: '制氢厂', remark: '单位制氢电耗' },
		{ parameter: '制氢基础单耗', value: params.h2_base_consumption_kwh_per_kg || 0, unit: 'kWh/kg', category: '制氢厂', remark: '基础单位制氢电耗' },
		{ parameter: '制氢设备服役年限', value: params.h2_equipment_service_years || 0, unit: '年', category: '制氢厂', remark: '制氢设备服役年限' },
		{ parameter: '制氢单耗年增长率', value: (params.h2_consumption_increase_annual || 0) * 100, unit: '%', category: '制氢厂', remark: '单耗年增长率' },
		{ parameter: '制氢用水价格', value: params.h2_water_price_yuan_per_ton || 0, unit: '元/吨', category: '制氢厂', remark: '工业用水价格' },
		{ parameter: '废水处理价格', value: params.h2_wastewater_price_yuan_per_ton || 0, unit: '元/吨', category: '制氢厂', remark: '废水处理费用' },
		{ parameter: '制氢厂员工数量', value: params.h2_staff_count || 0, unit: '人', category: '制氢厂', remark: '制氢厂员工数量' },
		{ parameter: '员工年薪', value: params.h2_staff_salary_wan_yuan_per_year || 0, unit: '万元/年', category: '制氢厂', remark: '员工平均年薪' },
		{ parameter: '氢气交通价格', value: params.h2_price_transport || 0, unit: '元/kg', category: '制氢厂', remark: '交通用氢销售价格' },
		{ parameter: '氢气化工价格', value: params.h2_price_chemical || 0, unit: '元/kg', category: '制氢厂', remark: '化工用氢销售价格' },
		{ parameter: '储氢投资', value: params.h2_storage_investment_wan_yuan || 0, unit: '万元', category: '制氢厂', remark: '储氢系统投资' },
		{ parameter: '储氢容量', value: params.h2_storage_capacity_tons || 0, unit: '吨', category: '制氢厂', remark: '氢气储存容量' },
		{ parameter: '制氢设备折旧年限', value: params.h2_equipment_depreciation_years || 0, unit: '年', category: '制氢厂', remark: '制氢设备折旧年限' },
		{ parameter: '氧气价格', value: params.o2_price_per_ton || 0, unit: '元/吨', category: '制氢厂', remark: '副产品氧气价格' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);
	
	// 5. 融资贷款模块
	data.push(
		{ parameter: '融资贷款', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '贷款期限', value: params.loan_term_years || 0, unit: '年', category: '融资贷款', remark: '项目贷款年限' },
		{ parameter: '贷款利率', value: (params.loan_interest_rate || 0) * 100, unit: '%', category: '融资贷款', remark: '年化贷款利率' },
		{ parameter: '土地租金', value: params.finance_land_rent_wan_yuan_per_year || 0, unit: '万元/年', category: '融资贷款', remark: '年度土地租金' },
		{ parameter: '贷款比例', value: (params.loan_ratio || 0) * 100, unit: '%', category: '融资贷款', remark: '项目融资比例' },
		{ parameter: '贷款总额', value: params.finance_loan_total_wan_yuan || 0, unit: '万元', category: '融资贷款', remark: '项目贷款总额' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);
	
	// 6. 液氨系统模块
	data.push(
		{ parameter: '液氨系统', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '液氨价格', value: params.ammonia_price_yuan_per_ton || 0, unit: '元/吨', category: '液氨系统', remark: '液氨市场价格' },
		{ parameter: '液氨消费量', value: params.ammonia_consumption_tons_per_hour || 0, unit: '吨/小时', category: '液氨系统', remark: '液氨小时消费量' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);
	
	// 7. 电化学储能电站模块
	data.push(
		{ parameter: '电化学储能电站', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '储能电站规模', value: params.electrochemical_energy_storage_scale_mw || 0, unit: 'MW', category: '储能电站', remark: '电化学储能装机规模' }
	);
	
	console.log('生成的能源数据条数:', data.length);
	return data;
});

// 直接从pinia store获取产品方案数据
const solutionData = computed(() => {
	if (!currentProject.value?.configuration_scheme) {
		console.warn('缺少 configuration_scheme 数据');
		return [];
	}

	const scheme = currentProject.value.configuration_scheme;
	const costComposition = scheme.cost_composition || {};
	console.log('产品方案数据:', scheme);
	console.log('成本构成数据:', costComposition);

	const data: Array<{ parameter: string; value: any; unit: string; category: string; remark: string; isHeader?: boolean; isSpacer?: boolean }> = [];

	// 1. 容量优化配置结果
	data.push(
		{ parameter: '容量优化配置结果', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '合成氨建设规模', value: (scheme as any).ammonia_construction_scale || 0, unit: '万吨/年', category: '配置结果', remark: '液氨年产能力' },
		{ parameter: '制氢系统规模', value: (scheme as any).hydrogen_system_scale || 0, unit: 'MW', category: '配置结果', remark: '制氢装机容量' },
		{ parameter: '电化学储能规模', value: (scheme as any).electrochemical_storage_scale || 0, unit: 'MW', category: '配置结果', remark: '储能装机容量' },
		{ parameter: '储氢规模', value: (scheme as any).hydrogen_storage_scale || 0, unit: 't', category: '配置结果', remark: '氢气储存容量' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);

	// 2. 经济性评价
	data.push(
		{ parameter: '经济性评价', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '风光氢内部收益率', value: (scheme as any).wind_solar_hydrogen_irr || 0, unit: '%', category: '经济性评价', remark: '风光制氢项目IRR' },
		{ parameter: '氢氨内部收益率', value: (scheme as any).hydrogen_ammonia_irr || 0, unit: '%', category: '经济性评价', remark: '制氢制氨项目IRR' },
		{ parameter: '全生命周期总利润', value: (scheme as any).total_lifecycle_profit || 0, unit: '万元', category: '经济性评价', remark: '项目全生命周期利润' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);

	// 3. 产品产量方案  
	data.push(
		{ parameter: '产品产量方案', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '年合成氨产量', value: (scheme as any).annual_ammonia_production || 0, unit: '万吨/年', category: '产量方案', remark: '年度液氨产量' },
		{ parameter: '年发电总量', value: (scheme as any).annual_total_power_generation || 0, unit: '万kWh', category: '产量方案', remark: '年度总发电量' },
		{ parameter: '上网电量', value: (scheme as any).grid_connected_power || 0, unit: '万kWh', category: '产量方案', remark: '年度并网电量' },
		{ parameter: '制氢电量', value: (scheme as any).hydrogen_production_power || 0, unit: '万kWh', category: '产量方案', remark: '年度制氢用电' },
		{ parameter: '制氢总量', value: (scheme as any).annual_hydrogen_production || 0, unit: 'Nm3', category: '产量方案', remark: '年度制氢产量' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);

	// 4. 成本构成
	data.push(
		{ parameter: '成本构成', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '光伏电站', value: costComposition.光伏电站 || 0, unit: '万元', category: '成本构成', remark: '光伏电站建设投资' },
		{ parameter: '风电站', value: costComposition.风电站 || 0, unit: '万元', category: '成本构成', remark: '风电站建设投资' },
		{ parameter: '光伏送出线路', value: costComposition.光伏送出线路 || 0, unit: '万元', category: '成本构成', remark: '光伏送出线路投资' },
		{ parameter: '风电送出线路', value: costComposition.风电送出线路 || 0, unit: '万元', category: '成本构成', remark: '风电送出线路投资' },
		{ parameter: '升压站', value: costComposition.升压站 || 0, unit: '万元', category: '成本构成', remark: '升压站建设投资' },
		{ parameter: '降压站', value: costComposition.降压站 || 0, unit: '万元', category: '成本构成', remark: '降压站建设投资' },
		{ parameter: '电池储能系统', value: costComposition.电池储能系统 || 0, unit: '万元', category: '成本构成', remark: '储能系统投资' },
		{ parameter: '制氢工厂', value: costComposition.制氢工厂 || 0, unit: '万元', category: '成本构成', remark: '制氢工厂建设投资' },
		{ parameter: '储氢系统', value: costComposition.储氢系统 || 0, unit: '万元', category: '成本构成', remark: '储氢系统投资' },
		{ parameter: '制氢公辅设施', value: costComposition.制氢公辅设施 || 0, unit: '万元', category: '成本构成', remark: '制氢公辅设施投资' },
		{ parameter: '其他1', value: costComposition.其他1 || 0, unit: '万元', category: '成本构成', remark: '其他投资费用1' },
		{ parameter: '其他2', value: costComposition.其他2 || 0, unit: '万元', category: '成本构成', remark: '其他投资费用2' },
		{ parameter: '其他3', value: costComposition.其他3 || 0, unit: '万元', category: '成本构成', remark: '其他投资费用3' },
		{ parameter: '其他4', value: costComposition.其他4 || 0, unit: '万元', category: '成本构成', remark: '其他投资费用4' },
		{ parameter: '其他5', value: costComposition.其他5 || 0, unit: '万元', category: '成本构成', remark: '其他投资费用5' },
		{ parameter: '', value: '', unit: '', category: '', remark: '', isSpacer: true }
	);

	// 5. 收入构成
	data.push(
		{ parameter: '收入构成', value: '', unit: '', category: '', remark: '', isHeader: true },
		{ parameter: '氢气', value: (scheme as any).hydrogen_sales_revenue || 0, unit: '万元', category: '收入构成', remark: '氢气销售收入' },
		{ parameter: '售电', value: (scheme as any).electricity_sales_revenue || 0, unit: '万元', category: '收入构成', remark: '上网电力收入' },
		{ parameter: '氨气', value: (scheme as any).ammonia_sales_revenue || 0, unit: '万元', category: '收入构成', remark: '液氨产品收入' }
	);

	console.log('生成的产品方案数据条数:', data.length);
	return data;
});

// 数值格式化函数
const formatNumber = (value) => {
	if (value === null || value === undefined || isNaN(value)) {
		return '0.00';
	}
	return Number(value).toFixed(2);
};

// Excel样式定义
const excelStyles = {
	// 能源项目测算表头样式
	energyHeader: {
		fill: { fgColor: { rgb: "E8F4FD" } },
		font: { bold: true, color: { rgb: "1890FF" }, sz: 12 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "thin", color: { rgb: "D9D9D9" } },
			bottom: { style: "thin", color: { rgb: "D9D9D9" } },
			left: { style: "thin", color: { rgb: "D9D9D9" } },
			right: { style: "thin", color: { rgb: "D9D9D9" } }
		}
	},
	// 产品方案表头样式
	solutionHeader: {
		fill: { fgColor: { rgb: "FFF2E8" } },
		font: { bold: true, color: { rgb: "FA8C16" }, sz: 12 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "thin", color: { rgb: "D9D9D9" } },
			bottom: { style: "thin", color: { rgb: "D9D9D9" } },
			left: { style: "thin", color: { rgb: "D9D9D9" } },
			right: { style: "thin", color: { rgb: "D9D9D9" } }
		}
	},
	// 现金流表头样式
	cashflowHeader: {
		fill: { fgColor: { rgb: "E8F5E8" } },
		font: { bold: true, color: { rgb: "2D5A2D" }, sz: 12 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "thin", color: { rgb: "67C23A" } },
			bottom: { style: "thin", color: { rgb: "67C23A" } },
			left: { style: "thin", color: { rgb: "67C23A" } },
			right: { style: "thin", color: { rgb: "67C23A" } }
		}
	},
	// 合计行样式
	totalRow: {
		fill: { fgColor: { rgb: "E8F5E8" } },
		font: { bold: true, color: { rgb: "2D5A2D" }, sz: 11 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "medium", color: { rgb: "67C23A" } },
			bottom: { style: "thin", color: { rgb: "67C23A" } },
			left: { style: "thin", color: { rgb: "67C23A" } },
			right: { style: "thin", color: { rgb: "67C23A" } }
		}
	},
	// 特殊行样式（所得税前净现金流、税后净现金流）
	specialRow: {
		fill: { fgColor: { rgb: "FFF7E6" } },
		font: { bold: true, color: { rgb: "D46B08" }, sz: 11 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "thin", color: { rgb: "FAAD14" } },
			bottom: { style: "thin", color: { rgb: "FAAD14" } },
			left: { style: "thin", color: { rgb: "FAAD14" } },
			right: { style: "thin", color: { rgb: "FAAD14" } }
		}
	},
	// 普通数据行样式
	dataRow: {
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "thin", color: { rgb: "EEEEEE" } },
			bottom: { style: "thin", color: { rgb: "EEEEEE" } },
			left: { style: "thin", color: { rgb: "EEEEEE" } },
			right: { style: "thin", color: { rgb: "EEEEEE" } }
		}
	},
	// 数值格式
	numberFormat: {
		numFmt: '#,##0.00'
	},
	// 模块标题行样式
	energyModuleHeader: {
		fill: { fgColor: { rgb: "E8F4FD" } },
		font: { bold: true, color: { rgb: "1890FF" }, sz: 14 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "medium", color: { rgb: "1890FF" } },
			bottom: { style: "thin", color: { rgb: "1890FF" } },
			left: { style: "thin", color: { rgb: "1890FF" } },
			right: { style: "thin", color: { rgb: "1890FF" } }
		}
	},
	// 产品方案模块标题行样式  
	solutionModuleHeader: {
		fill: { fgColor: { rgb: "FFF2E8" } },
		font: { bold: true, color: { rgb: "FA8C16" }, sz: 14 },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "medium", color: { rgb: "FA8C16" } },
			bottom: { style: "thin", color: { rgb: "FA8C16" } },
			left: { style: "thin", color: { rgb: "FA8C16" } },
			right: { style: "thin", color: { rgb: "FA8C16" } }
		}
	},
	// 空行样式
	spacerRow: {
		fill: { fgColor: { rgb: "FFFFFF" } },
		alignment: { horizontal: "center", vertical: "center" },
		border: {
			top: { style: "thin", color: { rgb: "FFFFFF" } },
			bottom: { style: "thin", color: { rgb: "FFFFFF" } },
			left: { style: "thin", color: { rgb: "FFFFFF" } },
			right: { style: "thin", color: { rgb: "FFFFFF" } }
		}
	}
};

// 应用样式到单元格
const applyCellStyle = (worksheet, cellRef, style) => {
	if (!worksheet[cellRef]) {
		worksheet[cellRef] = { t: 's', v: '' };
	}
	worksheet[cellRef].s = style;
};

// 直接使用 cashFlowAdapter 处理现金流数据
const adaptedCashFlowData = computed(() => {
	const cashFlowData = currentProject.value?.cash_flow || [];
	return adaptCashFlowData(cashFlowData);
});

// 获取现金流年份列表
const cashFlowYearColumns = computed(() => {
	const cashFlowData = currentProject.value?.cash_flow || [];
	return getYearColumns(cashFlowData);
});

// 检查是否有建设期数据
const hasCashFlowConstructionPeriod = computed(() => {
	const cashFlowData = currentProject.value?.cash_flow || [];
	return hasConstructionPeriod(cashFlowData);
});

// 合并所有现金流数据用于导出
const exportCashFlowData = computed(() => {
	const adaptedData = adaptedCashFlowData.value;
	if (!adaptedData || Object.keys(adaptedData).length === 0) {
		return [];
	}

	// 按照 Cashflow.vue 的布局合并数据
	return adaptedData.all || [];
});

// 获取现金流年份列表（包含建设期）
const allYears = computed(() => {
	const cashFlowData = currentProject.value?.cash_flow || [];
	if (cashFlowData.length === 0) {
		return Array.from({ length: 25 }, (_, i) => i + 1);
	}

	// 获取所有年份并排序，包括负数年份（建设期）
	const years = (cashFlowData as any[])
		.map(item => item.year)
		.filter(year => year !== undefined && year !== null && year !== 0) // 排除 year=0 的合计数据
		.sort((a, b) => a - b);

	return years.length > 0 ? years : Array.from({ length: 25 }, (_, i) => i + 1);
});

// 下载Excel文件
const downloadExcel = async () => {
	downloading.value = true;
	try {
		console.log('开始生成Excel文件');

		if (!hasValidData.value) {
			ElMessage.error('没有可导出的数据，请确保已选择项目');
			return;
		}

		// 创建新的工作簿
		const wb = XLSX.utils.book_new();
		let hasAnyData = false;

		// Sheet 1: 能源项目测算
		console.log('生成能源项目测算sheet，数据条数:', energyDataCombined.value.length);
		if (energyDataCombined.value.length > 0) {
			// 过滤掉显示用的属性，只保留导出需要的数据
			const cleanEnergyData = energyDataCombined.value.map(row => ({
				parameter: row.parameter,
				value: row.value,
				unit: row.unit,
				category: row.category,
				remark: row.remark
			}));
			
			const energySheet = XLSX.utils.json_to_sheet(cleanEnergyData);
			
			// 设置表头
			const headers = ['parameter', 'value', 'unit', 'category', 'remark'];
			const headerNames = ['参数名称', '数值', '单位', '类别', '备注'];
			headers.forEach((header, index) => {
				const cellRef = XLSX.utils.encode_cell({ c: index, r: 0 });
				if (energySheet[cellRef]) {
					energySheet[cellRef].v = headerNames[index];
				}
				applyCellStyle(energySheet, cellRef, excelStyles.energyHeader);
			});
			
			// 设置数据行样式
			energyDataCombined.value.forEach((row, rowIndex) => {
				const actualRowIndex = rowIndex + 1; // +1 因为第0行是表头
				headers.forEach((header, colIndex) => {
					const cellRef = XLSX.utils.encode_cell({ c: colIndex, r: actualRowIndex });
					
					// 根据行类型设置不同样式
					let cellStyle = excelStyles.dataRow;
					if (row.isHeader) {
						cellStyle = excelStyles.energyModuleHeader;
					} else if (row.isSpacer) {
						cellStyle = excelStyles.spacerRow;
					} else if (colIndex === 1) { // 数值列应用数字格式
						cellStyle = { ...excelStyles.dataRow, ...excelStyles.numberFormat };
					}
					
					applyCellStyle(energySheet, cellRef, cellStyle);
				});
			});
			
			energySheet['!cols'] = [
				{ wch: 20 }, // 参数名称
				{ wch: 15 }, // 数值
				{ wch: 12 }, // 单位
				{ wch: 15 }, // 类别
				{ wch: 30 }, // 备注
			];
			XLSX.utils.book_append_sheet(wb, energySheet, '能源项目测算');
			hasAnyData = true;
		} else {
			// 创建空的sheet但包含表头
			const emptyEnergyData = [
				{ parameter: '暂无数据', value: '', unit: '', category: '', remark: '请先在能源计算页面输入参数' }
			];
			const emptyEnergySheet = XLSX.utils.json_to_sheet(emptyEnergyData);
			
			// 设置表头
			const headers = ['parameter', 'value', 'unit', 'category', 'remark'];
			const headerNames = ['参数名称', '数值', '单位', '类别', '备注'];
			headers.forEach((header, index) => {
				const cellRef = XLSX.utils.encode_cell({ c: index, r: 0 });
				if (emptyEnergySheet[cellRef]) {
					emptyEnergySheet[cellRef].v = headerNames[index];
				}
				applyCellStyle(emptyEnergySheet, cellRef, excelStyles.energyHeader);
			});
			
			XLSX.utils.book_append_sheet(wb, emptyEnergySheet, '能源项目测算');
			hasAnyData = true;
		}

		// Sheet 2: 产品方案
		console.log('生成产品方案sheet，数据条数:', solutionData.value.length);
		if (solutionData.value.length > 0) {
			// 过滤掉显示用的属性，只保留导出需要的数据
			const cleanSolutionData = solutionData.value.map(row => ({
				parameter: row.parameter,
				value: row.value,
				unit: row.unit,
				category: row.category,
				remark: row.remark
			}));
			
			const solutionSheet = XLSX.utils.json_to_sheet(cleanSolutionData);
			
			// 设置表头
			const headers = ['parameter', 'value', 'unit', 'category', 'remark'];
			const headerNames = ['方案参数', '数值', '单位', '类别', '备注'];
			headers.forEach((header, index) => {
				const cellRef = XLSX.utils.encode_cell({ c: index, r: 0 });
				if (solutionSheet[cellRef]) {
					solutionSheet[cellRef].v = headerNames[index];
				}
				applyCellStyle(solutionSheet, cellRef, excelStyles.solutionHeader);
			});
			
			// 设置数据行样式
			solutionData.value.forEach((row, rowIndex) => {
				const actualRowIndex = rowIndex + 1; // +1 因为第0行是表头
				headers.forEach((header, colIndex) => {
					const cellRef = XLSX.utils.encode_cell({ c: colIndex, r: actualRowIndex });
					
					// 根据行类型设置不同样式
					let cellStyle = excelStyles.dataRow;
					if (row.isHeader) {
						cellStyle = excelStyles.solutionModuleHeader;
					} else if (row.isSpacer) {
						cellStyle = excelStyles.spacerRow;
					} else if (colIndex === 1) { // 数值列应用数字格式
						cellStyle = { ...excelStyles.dataRow, ...excelStyles.numberFormat };
					}
					
					applyCellStyle(solutionSheet, cellRef, cellStyle);
				});
			});
			
			solutionSheet['!cols'] = [
				{ wch: 20 }, // 方案名称/参数
				{ wch: 15 }, // 数值
				{ wch: 12 }, // 单位
				{ wch: 15 }, // 类别
				{ wch: 30 }, // 备注
			];
			XLSX.utils.book_append_sheet(wb, solutionSheet, '产品方案');
			hasAnyData = true;
		} else {
			// 创建空的sheet但包含表头
			const emptySolutionData = [
				{ parameter: '暂无数据', value: '', unit: '', category: '', remark: '请先在产品方案页面配置数据' }
			];
			const emptySolutionSheet = XLSX.utils.json_to_sheet(emptySolutionData);
			
			// 设置表头
			const headers = ['parameter', 'value', 'unit', 'category', 'remark'];
			const headerNames = ['方案参数', '数值', '单位', '类别', '备注'];
			headers.forEach((header, index) => {
				const cellRef = XLSX.utils.encode_cell({ c: index, r: 0 });
				if (emptySolutionSheet[cellRef]) {
					emptySolutionSheet[cellRef].v = headerNames[index];
				}
				applyCellStyle(emptySolutionSheet, cellRef, excelStyles.solutionHeader);
			});
			
			XLSX.utils.book_append_sheet(wb, emptySolutionSheet, '产品方案');
			hasAnyData = true;
		}

		// Sheet 3: 现金流量分析
		console.log('生成现金流量分析sheet');
		const exportData = exportCashFlowData.value;

		if (exportData.length > 0) {
			console.log('现金流数据总条数:', exportData.length);

			// 过滤掉分隔行（isSpacer: true）
			const filteredCashFlowData = exportData.filter(item => !item.isSpacer);
			
			// 构建表头和数据结构
			const headers = ['项目名称', '合计'];
			
			// 如果有建设期，添加建设期列
			if (hasCashFlowConstructionPeriod.value) {
				headers.push('-2', '-1');
			}
			
			// 添加运营期年份列
			cashFlowYearColumns.value.forEach(year => {
				headers.push(year.toString());
			});

			// 重新组织数据，确保列顺序正确
			const organizedData = filteredCashFlowData.map(row => {
				const organizedRow = {};
				headers.forEach(header => {
					organizedRow[header] = row[header] || '0.00';
				});
				return organizedRow;
			});

			// 创建工作表
			const cashFlowSheet = XLSX.utils.json_to_sheet(organizedData, { header: headers });
			
			// 设置表头样式
			headers.forEach((header, index) => {
				const cellRef = XLSX.utils.encode_cell({ c: index, r: 0 });
				if (!cashFlowSheet[cellRef]) cashFlowSheet[cellRef] = { t: 's', v: header };
				applyCellStyle(cashFlowSheet, cellRef, excelStyles.cashflowHeader);
			});

			// 设置数据行样式
			organizedData.forEach((row, rowIndex) => {
				const actualRowIndex = rowIndex + 1; // +1 因为第0行是表头
				const originalRow = filteredCashFlowData[rowIndex];
				
				headers.forEach((header, colIndex) => {
					const cellRef = XLSX.utils.encode_cell({ c: colIndex, r: actualRowIndex });
					if (!cashFlowSheet[cellRef]) {
						const cellValue = row[header];
						if (colIndex > 0 && cellValue !== undefined) { // 除了项目名称列外的其他列
							cashFlowSheet[cellRef] = { t: 'n', v: parseFloat(cellValue) || 0 };
						} else {
							cashFlowSheet[cellRef] = { t: 's', v: cellValue || '' };
						}
					}
					
					// 根据行类型应用不同样式
					let cellStyle = excelStyles.dataRow;
					
					if (originalRow && originalRow.isTotal) {
						// 合计行样式
						cellStyle = excelStyles.totalRow;
					} else if (originalRow && (
						originalRow['项目名称'] === '所得税前净现金流' || 
						originalRow['项目名称'] === '税后净现金流'
					)) {
						// 特殊行样式：所得税前净现金流、税后净现金流
						cellStyle = excelStyles.specialRow;
					}
					
					// 为数值列添加数字格式
					if (colIndex > 0) {
						cellStyle = { ...cellStyle, ...excelStyles.numberFormat };
					}
					
					applyCellStyle(cashFlowSheet, cellRef, cellStyle);
				});
			});

			// 设置列宽
			const columnWidths = [
				{ wch: 25 }, // 项目名称
				{ wch: 15 }, // 合计
			];
			
			// 如果有建设期，添加建设期列的宽度设置
			if (hasCashFlowConstructionPeriod.value) {
				columnWidths.push({ wch: 12 }); // -2 年
				columnWidths.push({ wch: 12 }); // -1 年
			}
			
			// 添加运营期年份列的宽度设置
			cashFlowYearColumns.value.forEach(() => {
				columnWidths.push({ wch: 12 });
			});
			
			cashFlowSheet['!cols'] = columnWidths;

			XLSX.utils.book_append_sheet(wb, cashFlowSheet, '现金流量分析');
			hasAnyData = true;
		} else {
			// 创建空的现金流sheet
			const emptyCashFlowData = [
				{ '项目名称': '暂无数据', '合计': '', '备注': '请先在现金流页面生成数据' }
			];
			const emptyCashFlowSheet = XLSX.utils.json_to_sheet(emptyCashFlowData);
			
			// 设置表头
			const headers = ['项目名称', '合计', '备注'];
			headers.forEach((header, index) => {
				const cellRef = XLSX.utils.encode_cell({ c: index, r: 0 });
				applyCellStyle(emptyCashFlowSheet, cellRef, excelStyles.cashflowHeader);
			});
			
			XLSX.utils.book_append_sheet(wb, emptyCashFlowSheet, '现金流量分析');
			hasAnyData = true;
		}

		// 确保工作簿不为空
		if (!hasAnyData) {
			const defaultSheet = XLSX.utils.json_to_sheet([
				{ '提示': '暂无可导出的数据', '说明': '请先在相关页面输入或生成数据' }
			]);
			XLSX.utils.book_append_sheet(wb, defaultSheet, '导出说明');
		}

		// 生成Excel文件
		console.log('开始生成Excel文件');
		const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
		const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

		// 创建下载链接
		const url = window.URL.createObjectURL(data);
		const link = document.createElement('a');
		link.href = url;

		// 处理项目名称
		const projectName = currentProjectInfo.value?.name || '当前页面数据';
		const timestamp = new Date().toISOString().slice(0, 10);
		link.download = `导出报告_${projectName}_${timestamp}.xlsx`;

		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		window.URL.revokeObjectURL(url);

		console.log('Excel文件下载完成:', link.download);
		ElMessage.success('Excel文件下载成功');
	} catch (error) {
		console.error('下载失败:', error);
		ElMessage.error('Excel文件生成失败: ' + error.message);
	} finally {
		downloading.value = false;
	}
};

// 页面初始化
onMounted(() => {
	console.log('导出预览页面初始化');
	console.log('当前项目数据:', currentProject.value ? '已加载' : '未加载');

	if (currentProject.value) {
		console.log('项目信息:', currentProjectInfo.value);
		console.log('完整项目数据结构:', currentProject.value);
		
		// 检查各部分数据是否存在
		console.log('operating_params:', currentProject.value.operating_params);
		console.log('fixed_investments:', currentProject.value.fixed_investments);
		console.log('energy_material_balance:', currentProject.value.energy_material_balance);
		console.log('cash_flow:', currentProject.value.cash_flow);
		
		console.log('能源数据条数:', energyDataCombined.value.length);
		console.log('产品方案条数:', solutionData.value.length);
		console.log('现金流数据条数:', exportCashFlowData.value.length);
	} else {
		console.warn('pinia store中没有项目数据');
		ElMessage.warning('没有检测到项目数据，请先在其他页面选择或创建项目');
	}
});
</script>

<style scoped>
.export-preview-page {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding: 20px;
}

.preview-header {
	background: white;
	padding: 20px;
	border-radius: 8px;
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 20px;
}

.preview-header h1 {
	margin: 0;
	color: #303133;
	font-size: 24px;
}

.project-info {
	display: flex;
	align-items: center;
	gap: 12px;
}

.project-time {
	font-size: 12px;
	color: #909399;
}

.header-actions {
	display: flex;
	gap: 12px;
}

.preview-content {
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	min-height: 600px;
}

.sheet-tabs {
	padding: 20px;
}

.sheet-content {
	padding: 20px 0;
}

.cashflow-section {
	margin-bottom: 30px;
}

.cashflow-section h3 {
	margin: 0 0 15px 0;
	color: #303133;
	font-size: 16px;
	font-weight: 600;
}

.table-container {
	overflow-x: auto;
	border: 1px solid #ebeef5;
	border-radius: 4px;
}

.cashflow-table {
	min-width: max-content;
}

.cashflow-table :deep(.el-table__header th) {
	background-color: #f8f9fa !important;
	color: #303133;
	font-weight: 600;
	text-align: center;
	border-bottom: 2px solid #e4e7ed;
}

.cashflow-table :deep(.el-table__body tr:hover > td) {
	background-color: #f5f7fa !important;
}

.cashflow-table :deep(.el-table__row--striped td) {
	background-color: #fafafa;
}

.cashflow-table :deep(.el-table__body td) {
	padding: 8px 12px;
	font-size: 13px;
}

.cashflow-table :deep(.el-table__body td:first-child) {
	font-weight: 500;
}

.cashflow-table :deep(.el-table__fixed-left) {
	box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

/* 合计行样式 */
.cashflow-table :deep(.total-row td) {
	background-color: #e8f5e8 !important;
	font-weight: bold;
	color: #2d5a2d;
	border-top: 2px solid #67c23a;
}

/* 分隔行样式 */
.cashflow-table :deep(.spacer-row td) {
	background-color: #f8f9fa !important;
	height: 20px;
	padding: 0 !important;
	border: none !important;
}

.cashflow-table :deep(.spacer-row td .cell) {
	height: 20px;
	line-height: 20px;
}

/* 模块标题行样式 */
:deep(.header-row td) {
	background-color: #e8f4fd !important;
	font-weight: bold !important;
	color: #1890ff !important;
	text-align: center !important;
	font-size: 14px !important;
	border-top: 2px solid #1890ff !important;
}

/* 空行分隔样式 */
:deep(.spacer-row td) {
	background-color: transparent !important;
	height: 15px !important;
	padding: 0 !important;
	border: none !important;
}

:deep(.spacer-row td .cell) {
	height: 15px;
	line-height: 15px;
}

.data-summary {
	margin-top: 15px;
	padding: 10px;
	background-color: #f8f9fa;
	border-radius: 4px;
	text-align: center;
}

:deep(.el-table) {
	font-size: 12px;
}

:deep(.el-table th) {
	background-color: #fafafa;
	font-weight: 600;
}

:deep(.el-table .el-table__cell) {
	padding: 8px 0;
}
</style>
