<template>
  <div class="product-solutions">
    <div class="header">
      <h2>产品方案</h2>
      <div class="controls">
        <el-button 
          :type="isEditable ? 'warning' : 'primary'" 
          @click="toggleEdit"
          :icon="isEditable ? Lock : Edit"
        >
          {{ isEditable ? '锁定' : '编辑' }}
        </el-button>
        <!-- <el-button type="success" @click="exportToExcel" :icon="Download">
          导出结果
        </el-button> -->
      </div>
    </div>

    <div class="content-wrapper">
      <el-card class="solution-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">可再生能源综合制甲醇高效利用系统</span>
          </div>
        </template>

        <div class="solution-content">
          <!-- 左侧流程图区域 -->
          <div class="flowchart-section">
            <div class="flowchart-container">
              <img 
                src="/public/images/flowchart.png" 
                alt="产品方案流程图" 
                class="flowchart-image" 
                @error="handleImageError" 
              />
            </div>
          </div>

          <!-- 右侧数据表格区域 -->
          <div class="data-section">
            <h4>配置结果</h4>
            <el-table :data="tableData" border class="result-table" :max-height="600">
              <el-table-column prop="parameter" label="参数名称" width="200">
                <template #default="{ row }">
                  <span class="parameter-name">{{ row.parameter }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="value" label="数值" width="150">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.value"
                    :precision="row.precision || 0"
                    :step="row.step || 1"
                    :min="row.min || 0"
                    :max="row.max"
                    :disabled="!isEditable"
                    size="small"
                    style="width: 100%"
                    controls-position="right"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="unit" label="单位" width="100">
                <template #default="{ row }">
                  <span>{{ row.unit }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="category"
                label="类别"
                width="120"
                :filters="categoryFilters"
                :filter-method="filterByCategory"
              >
                <template #default="{ row }">
                  <el-tag
                    :type="getCategoryType(row.category)"
                    :color="getCategoryColor(row.category)"
                    size="small"
                  >
                    {{ row.category }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="备注" min-width="200">
                <template #default="{ row }">
                  <span class="remark">{{ row.remark }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Lock, Download } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { useProjectStore } from '@/store/modules/project'

// 控制编辑状态
const isEditable = ref(false)
const projectStore = useProjectStore()

// 类别筛选选项
const categoryFilters = ref([
  { text: '容量优化配置结果', value: '容量优化配置结果' },
  { text: '总体产品方案', value: '总体产品方案' },
  { text: '系统经济性连算结果', value: '系统经济性连算结果' },
  { text: '经济性评价', value: '经济性评价' },
  { text: '成本构成', value: '成本构成' },
  { text: '收入结构', value: '收入结构' }
])

// 类别筛选方法
const filterByCategory = (value, row, column) => {
  return row.category === value
}

// 字段映射关系
const fieldMapping = {
  // 容量优化配置结果
  'ammonia_construction_scale': { category: '容量优化配置结果', name: '合成氨装置规模', unit: '万吨/年', precision: 0 },
  'hydrogen_system_scale': { category: '容量优化配置结果', name: '氨合成系统规模', unit: 'MW', precision: 0 },
  'electrochemical_storage_scale': { category: '容量优化配置结果', name: '电化学储能规模', unit: 'MW', precision: 0 },
  'hydrogen_storage_scale': { category: '容量优化配置结果', name: '储氢规模', unit: 't', precision: 0 },
  
  // 总体产品方案
  'annual_ammonia_production': { category: '总体产品方案', name: '年合成氨产量', unit: '万吨/年', precision: 0 },
  'annual_total_power_generation': { category: '总体产品方案', name: '年发电总电量', unit: '万kWh', precision: 0 },
  'grid_connected_power': { category: '总体产品方案', name: '上网电量', unit: '万kWh', precision: 0 },
  'hydrogen_production_power': { category: '总体产品方案', name: '储氢电量', unit: '万kWh', precision: 0 },
  'annual_hydrogen_production': { category: '总体产品方案', name: '年制氢总量', unit: '万Nm3', precision: 2 },
  
  // 系统经济性连算结果
  'project_cycle_years': { category: '系统经济性连算结果', name: '项目周期', unit: '年', precision: 0 },
  'total_lifecycle_profit': { category: '系统经济性连算结果', name: '全生命周期总利润', unit: '万元', precision: 0 },
  
  // 经济性评价
  'project_cycle_years_eval': { category: '经济性评价', name: '项目周期', unit: '年', precision: 0 },
  'hydrogen_ammonia_irr': { category: '经济性评价', name: '氨气内部收益率', unit: '%', precision: 2 },
  // 'ammonia_npv': { category: '经济性评价', name: '氨气净现值', unit: '%', precision: 2 }, // 需要后端添加
  'lifecycle_profit': { category: '经济性评价', name: '生命周期利润', unit: '万元', precision: 0 },
  
  // 收入结构
  'oxygen_sales_revenue': { category: '收入结构', name: '售氧', unit: '万元', precision: 0 },
  'electricity_sales_revenue': { category: '收入结构', name: '售电', unit: '万元', precision: 0 },
  'ammonia_sales_revenue': { category: '收入结构', name: '售氨', unit: '万元', precision: 0 }
}

// 从配置方案生成表格数据
const generateTableDataFromConfig = () => {
  const config = projectStore.currentProject?.configuration_scheme
  if (!config) return []

  const data = []
  
  // 添加基础字段数据
  Object.keys(fieldMapping).forEach(key => {
    const mapping = fieldMapping[key]
    let value = config[key] || 0
    
    // 特殊处理项目周期在经济性评价中的重复
    if (key === 'project_cycle_years_eval') {
      value = config['project_cycle_years'] || 0
    }
    
    // 百分比转换
    if (mapping.unit === '%' && key.includes('irr')) {
      value = (value * 100).toFixed(mapping.precision)
    }

    data.push({
      parameter: mapping.name,
      value: Number(value),
      unit: mapping.unit,
      category: mapping.category,
      remark: getRemarkForField(mapping.name),
      precision: mapping.precision,
      step: mapping.precision === 0 ? 1 : 0.01,
      min: 0
    })
  })
  
  // 添加成本构成数据
  if (config.cost_composition) {
    Object.keys(config.cost_composition).forEach(key => {
      data.push({
        parameter: key,
        value: config.cost_composition[key] || 0,
        unit: '万元',
        category: '成本构成',
        remark: getCostRemarkForField(key),
        precision: 0,
        step: 100,
        min: 0
      })
    })
  }
  
  return data
}

// 获取字段备注信息
const getRemarkForField = (fieldName) => {
  const remarkMap = {
    '合成氨装置规模': '合成氨生产装置年产能规模',
    '氨合成系统规模': '氨合成系统装机容量',
    '电化学储能规模': '电化学储能系统装机规模',
    '储氢规模': '氢气储存容量规模',
    '年合成氨产量': '年度合成氨实际产量',
    '年发电总电量': '年度发电总量',
    '上网电量': '输送到电网的电量',
    '储氢电量': '用于储氢的电量',
    '年制氢总量': '年度制氢总产量',
    '项目周期': '项目运营周期',
    '全生命周期总利润': '项目全生命周期总利润',
    '氨气内部收益率': '氨气项目内部收益率',
    '氨气净现值': '氨气项目净现值',
    '生命周期利润': '项目生命周期总利润',
    '售氧': '售氧收入',
    '售电': '售电收入',
    '售氨': '售氨收入'
  }
  return remarkMap[fieldName] || ''
}

// 获取成本项目备注信息
const getCostRemarkForField = (fieldName) => {
  const costRemarkMap = {
    '总计': '总成本',
    '光伏电站': '光伏发电系统成本',
    '风电站': '风力发电系统成本',
    '光伏送出线路': '光伏电力输送线路成本',
    '风电送出线路': '风电输送线路成本',
    '升压站': '升压站建设成本',
    '降压站': '降压站建设成本',
    '电池储能系统': '电池储能系统成本',
    '储氢系统': '储氢系统建设成本',
    '制氢工厂': '制氢系统建设成本',
    '制氢公辅设施': '制氢公用设施成本',
    '其他1': '其他成本项目1',
    '其他2': '其他成本项目2',
    '其他3': '其他成本项目3',
    '其他4': '其他成本项目4',
    '其他5': '其他成本项目5'
  }
  return costRemarkMap[fieldName] || ''
}

// 响应式表格数据
const tableData = computed(() => {
  return generateTableDataFromConfig()
})

// 监听项目变化，重新生成表格数据
watch(() => projectStore.currentProject, () => {
  // 当项目数据变化时，表格数据会自动更新（因为使用了computed）
}, { deep: true })

// 组件挂载时确保有项目数据
onMounted(() => {
  if (!projectStore.currentProject || !projectStore.currentProject.configuration_scheme) {
    // 如果没有项目数据，可以触发加载
    console.warn('没有找到项目配置方案数据')
  }
})

// 获取类别标签类型
const getCategoryType = (category) => {
  const typeMap = {
    '容量优化配置结果': 'primary',
    '总体产品方案': 'success',
    '系统经济性连算结果': 'warning',
    '经济性评价': 'info',
    '成本构成': 'danger',
  }
  return typeMap[category] || 'primary'
}

// 获取类别标签自定义颜色
const getCategoryColor = (category) => {
  if (category === '收入结构') {
    return '#e4e7b6' // 紫色
  }
  return undefined
}

// 处理图片加载错误
const handleImageError = (e) => {
  e.target.src = '/placeholder-flowchart.png' // 可以设置一个默认的占位图
}

// 切换编辑状态
const toggleEdit = () => {
  isEditable.value = !isEditable.value
  ElMessage({
    type: 'success',
    message: isEditable.value ? '已开启编辑模式' : '已锁定数据'
  })
}

// 导出Excel功能
const exportToExcel = () => {
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 准备数据
    const data = [
      ['产品方案配置结果', '', '', '', ''],
      ['参数名称', '数值', '单位', '类别', '备注'],
      ...tableData.value.map(item => [
        item.parameter,
        item.value,
        item.unit,
        item.category,
        item.remark
      ])
    ]
    
    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(data)
    
    // 设置列宽
    ws['!cols'] = [
      { wch: 20 },
      { wch: 15 },
      { wch: 12 },
      { wch: 15 },
      { wch: 25 }
    ]
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '产品方案配置结果')
    
    // 生成文件名
    const fileName = `产品方案配置结果_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`
    
    // 导出文件
    XLSX.writeFile(wb, fileName)
    
    ElMessage({
      type: 'success',
      message: '导出成功！'
    })
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage({
      type: 'error',
      message: '导出失败，请重试'
    })
  }
}

</script>

<style scoped>
.product-solutions {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.controls {
  display: flex;
  gap: 12px;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.solution-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.solution-content {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 20px;
  min-height: 500px;
  padding: 20px;
}

@media (max-width: 1200px) {
  .solution-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.flowchart-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 10px;
  background-color: #fafafa;
}

.flowchart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.flowchart-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 4px;
}

.data-section h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.result-table {
  width: 100%;
}

.parameter-name {
  font-weight: 500;
}

.remark {
  color: #606266;
  font-size: 12px;
}

.el-card :deep(.el-card__header) {
  background-color: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 20px;
}

.el-card :deep(.el-card__body) {
  padding: 0;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .controls {
    width: 100%;
    justify-content: flex-end;
  }
  
  .solution-content {
    grid-template-columns: 1fr;
    padding: 10px;
  }
}
</style>
