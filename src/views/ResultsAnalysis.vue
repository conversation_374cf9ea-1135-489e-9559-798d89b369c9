<template>
  <div class="results-page">
    <div class="page-header">
      <h1>经济测算结果分析</h1>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Download" 
          @click="exportToPDF"
          :loading="isExporting"
        >
          导出PDF报告
        </el-button>
      </div>
    </div>

    <div class="results-content">

      <!-- 关键指标总览 -->
      <el-row :gutter="20" class="kpi-overview">
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatNumber(kpiData.totalInvestment) }}</div>
              <div class="kpi-label">总投资 (万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatNumber(kpiData.totalCapacity, 1) }}</div>
              <div class="kpi-label">总装机容量 (MW)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatPercent(kpiData.averageIRR) }}</div>
              <div class="kpi-label">平均内部收益率</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatNumber(kpiData.totalNPV) }}</div>
              <div class="kpi-label">总净现值 (万元)</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表展示区域 -->
      <el-row :gutter="20" class="charts-section">
        <!-- 投资构成饼图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>投资构成分析</h3>
            </template>
            <v-chart
              class="chart"
              :option="investmentCompositionOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>

        <!-- 收益率对比柱图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>各项目收益率对比</h3>
            </template>
            <v-chart
              class="chart"
              :option="irrComparisonOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>
      </el-row>

      

      <el-row :gutter="20" class="charts-section">
        <!-- 现金流分析线图 -->
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="chart-header">
                <h3>现金流分析</h3>
                <div class="chart-controls">
                  <el-radio-group v-model="taxType" style="margin-right: 20px;">
                    <el-radio label="after_tax" value="after_tax">税后</el-radio>
                    <el-radio label="before_tax" value="before_tax">税前</el-radio>
                  </el-radio-group>
                  <el-radio-group v-model="cashflowType">
                    <el-radio label="cumulative" value="cumulative">累计现金流</el-radio>
                    <el-radio label="annual" value="annual">年度现金流</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <v-chart
              class="chart-large"
              :option="cashflowOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>
      </el-row>

      
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
// @ts-ignore
import { useProjectStore } from '@/store/modules/project.js'
// @ts-ignore
import * as ResultsAdapter from '@/utils/resultsDataAdapter.js'

import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import {
  PieChart,
  BarChart,
  LineChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const route = useRoute()
const router = useRouter()
const projectStore = useProjectStore()

// 数据状态
const cashflowType = ref<'cumulative' | 'annual'>('cumulative')
const taxType = ref<'after_tax' | 'before_tax'>('after_tax')  // 默认显示税后数据
const isExporting = ref<boolean>(false)



// KPI 数据 - 使用计算属性从项目数据中获取
const kpiData = computed(() => {
  if (!projectStore.currentProject) {
    return {
      totalInvestment: 0,
      totalCapacity: 0,
      averageIRR: 0,
      totalNPV: 0
    };
  }
  return ResultsAdapter.calculateKPIs(projectStore.currentProject);
});

// 工具函数暴露给模板使用
const formatNumber = ResultsAdapter.formatNumber;
const formatPercent = ResultsAdapter.formatPercent;


// 图表配置
const investmentCompositionOption = computed(() => {
  const compositionData = projectStore.currentProject 
    ? ResultsAdapter.getInvestmentComposition(projectStore.currentProject) 
    : [];
    
  return {
    title: {
      text: '投资构成',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}万元 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '投资构成',
        type: 'pie',
        radius: '50%',
        data: compositionData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
});

const irrComparisonOption = computed(() => {
  const moduleData = projectStore.currentProject 
    ? ResultsAdapter.getModuleROIComparison(projectStore.currentProject) 
    : { categories: [], data: [] };
    
  return {
    title: {
      text: '各模块收益率对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: moduleData.categories
    },
    yAxis: {
      type: 'value',
      name: '收益率(%)'
    },
    series: [
      {
        name: '收益率',
        type: 'bar',
        data: moduleData.data,
        itemStyle: {
          color: function(params: any) {
            const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
            return colors[params.dataIndex % colors.length];
          }
        }
      }
    ]
  };
});

const cashflowOption = computed(() => {
  const cashflowData = projectStore.currentProject 
    ? ResultsAdapter.getCashflowData(projectStore.currentProject, cashflowType.value, taxType.value) 
    : { categories: [], series: [] };
    
  return {
    title: {
      text: '项目现金流分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        if (params && params.length > 0) {
          const param = params[0];
          return `${param.axisValue}<br/>${param.seriesName}: ${formatNumber(param.value)}万元`;
        }
        return '';
      }
    },
    legend: {
      data: cashflowData.series.map((s: any) => s.name),
      top: 30
    },
    xAxis: {
      type: 'category',
      data: cashflowData.categories
    },
    yAxis: {
      type: 'value',
      name: '现金流(万元)'
    },
    series: cashflowData.series
  };
});



// 现金流图表更新监听
watch([cashflowType, taxType], () => {
  // 当现金流类型或税类型改变时，computed属性会自动重新计算
});

// 组件挂载时确保有项目数据
onMounted(async () => {
  if (!projectStore.currentProject && projectStore.projectHistory.length > 0) {
    await projectStore.fetchProjectById(projectStore.projectHistory[0].id);
  }
});

// 页面操作
const goBack = (): void => {
  router.push('/calculation/energy')
}

const exportReport = (): void => {
  // 获取当前项目ID
  const projectId = route.query.projectId || route.params.projectId || 1
  
  // 打开Excel预览页面
  const previewUrl = `/export-preview?projectId=${projectId}`
  window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
}

// PDF导出功能
const exportToPDF = async (): Promise<void> => {
  try {
    isExporting.value = true
    ElMessage.info('正在生成PDF报告，请稍候...')

    // 动态导入html2canvas和jsPDF
    const html2canvas = (await import('html2canvas')).default as any
    const jsPDF = (await import('jspdf')).default as any

    // 获取整个页面元素（包括标题）
    const pageElement = document.querySelector('.results-page') as HTMLElement
    if (!pageElement) {
      throw new Error('未找到页面元素')
    }

    // 保存原始样式
    const originalStyles = {
      overflow: document.body.style.overflow,
      width: pageElement.style.width,
      height: pageElement.style.height
    }
    
    // 优化样式用于截图
    document.body.style.overflow = 'visible'
    pageElement.style.width = '1200px'
    pageElement.style.height = 'auto'
    
    // 确保所有图表都能完整显示
    const charts = pageElement.querySelectorAll('.chart, .chart-large') as NodeListOf<HTMLElement>
    charts.forEach(chart => {
      chart.style.minHeight = chart.classList.contains('chart-large') ? '400px' : '350px'
    })
    
    // 触发窗口resize事件来重新渲染图表
    const resizeEvent = new Event('resize')
    window.dispatchEvent(resizeEvent)
    
    // 等待图表重新渲染完成
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 2500))

    // 使用优化的html2canvas配置进行截图
    const canvas = await html2canvas(pageElement, {
      scale: 1.5, // 平衡质量和性能
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 1200,
      height: pageElement.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      logging: false,
      onclone: (clonedDoc) => {
        // 在克隆的文档中确保图表正确显示
        const clonedCharts = clonedDoc.querySelectorAll('.chart, .chart-large') as NodeListOf<HTMLElement>
        clonedCharts.forEach(chart => {
          chart.style.width = '100%'
          chart.style.minHeight = chart.classList.contains('chart-large') ? '400px' : '350px'
          chart.style.display = 'block'
        })
      }
    })

    // 恢复原始样式
    Object.assign(pageElement.style, originalStyles)
    document.body.style.overflow = originalStyles.overflow
    window.dispatchEvent(resizeEvent)

    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = 210
    const pageHeight = 297
    const margin = 10
    
    // 计算图片尺寸以适应PDF页面
    const maxWidth = pageWidth - (margin * 2)
    const maxHeight = pageHeight - (margin * 2)
    
    const imgAspectRatio = canvas.width / canvas.height
    let imgWidth = maxWidth
    let imgHeight = imgWidth / imgAspectRatio
    
    // 如果高度超出页面，调整尺寸
    if (imgHeight > maxHeight) {
      imgHeight = maxHeight
      imgWidth = imgHeight * imgAspectRatio
    }
    
    // 转换图片数据
    const imgData = canvas.toDataURL('image/png', 1.0)
    
    // 计算居中位置
    const xPos = (pageWidth - imgWidth) / 2
    const yPos = margin
    
    // 判断是否需要分页
    const totalPages = Math.ceil(imgHeight / maxHeight)
    
    for (let page = 0; page < totalPages; page++) {
      if (page > 0) {
        pdf.addPage()
      }
      
      // 计算当前页面应该显示的图片部分
      const sourceY = page * maxHeight * (canvas.height / imgHeight)
      const sourceHeight = Math.min(maxHeight * (canvas.height / imgHeight), canvas.height - sourceY)
      const displayHeight = sourceHeight * (imgHeight / canvas.height)
      
      // 创建当前页面的图片切片
      const pageCanvas = document.createElement('canvas')
      const pageCtx = pageCanvas.getContext('2d')
      pageCanvas.width = canvas.width
      pageCanvas.height = sourceHeight
      
      // 绘制当前页面的内容
      pageCtx?.drawImage(canvas, 0, -sourceY)
      
      const pageImgData = pageCanvas.toDataURL('image/png', 1.0)
      pdf.addImage(pageImgData, 'PNG', xPos, yPos, imgWidth, displayHeight)
    }

    // 保存PDF文件
    const fileName = `经济测算结果分析_${new Date().toISOString().slice(0, 10)}.pdf`
    pdf.save(fileName)

    ElMessage.success('PDF报告导出成功！')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败，请重试')
  } finally {
    isExporting.value = false
  }
}


</script>

<style scoped>
.results-page {
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  color: #303133;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.kpi-overview {
  margin-bottom: 30px;
}

.kpi-card {
  text-align: center;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.kpi-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.kpi-content {
  padding: 20px 0;
}

.kpi-value {
  font-size: 28px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.kpi-label {
  font-size: 14px;
  color: #606266;
}

.charts-section {
  margin-bottom: 30px;
}

.chart {
  height: 350px;
  width: 100%;
}

.chart-large {
  height: 400px;
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.data-info-section {
  margin-bottom: 20px;
}

.api-test-section {
  margin-bottom: 30px;
}

.api-test-section .el-card {
  border: 1px solid #e4e7ed;
}

.api-test-section .el-button {
  margin-left: 8px;
}

.data-table-section {
  margin-top: 30px;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__header h3) {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 现金流量表样式 */
.data-analysis-table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 600px;
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.data-analysis-table {
  min-width: max-content;
  width: auto;
}

.data-analysis-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.data-analysis-table :deep(.el-table__header th) {
  background-color: #f8f9fa !important;
  color: #303133;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.data-analysis-table :deep(.el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

.data-analysis-table :deep(.el-table__row--striped td) {
  background-color: #fafafa;
}

.data-analysis-table :deep(.el-table__row[data-parent="true"] td) {
  background-color: #e8f5e8 !important;
  font-weight: bold;
  color: #2d5a2d;
}

.data-analysis-table :deep(.el-table__row[data-child="true"] td) {
  background-color: #f9f9f9 !important;
  padding-left: 20px;
}

.data-analysis-table :deep(.el-table__body td:nth-child(1)),
.data-analysis-table :deep(.el-table__header th:nth-child(1)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(2)) {
  text-align: left !important;
}

.data-analysis-table :deep(.el-table__header th:nth-child(2)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(3)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header th:nth-child(3)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(n+4)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th[colspan] {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th[colspan] .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header-wrapper) .el-table__header thead tr th {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header-wrapper) .el-table__header thead tr th .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(n+6)) {
  text-align: right !important;
}

.data-analysis-table :deep(.el-table__body tr) td:first-child {
  vertical-align: middle;
}

.data-analysis-table :deep(.el-table__body .special-first-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-second-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-third-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-fourth-row td) {
  background-color: #ffffff !important;
}
.data-analysis-table :deep(.el-table__body .special-fifth-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body td[rowspan]) {
  vertical-align: middle !important;
  text-align: center !important;
  font-weight: bold;
}

.data-analysis-table :deep(.el-table__body .special-first-row td:first-child) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: bold;
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-third-row td:first-child) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: bold;
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-first-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-second-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-third-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-fourth-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-fifth-row td:nth-child(2)) {
  text-align: left !important;
  padding-left: 20px;
}

.data-analysis-table :deep(.el-table__header th:nth-child(1) .el-table__cell) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header th),
.data-analysis-table :deep(.el-table__header .el-table__cell) {
  text-align: center !important;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .kpi-overview .el-col {
    margin-bottom: 16px;
  }
  
  .charts-section .el-col {
    margin-bottom: 20px;
  }
}

/* PDF导出优化样式 */
@media print {
  .results-page {
    background: white;
  }
  
  .page-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
  }
  
  .kpi-card,
  .el-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ddd;
  }
  
  .chart,
  .chart-large {
    break-inside: avoid;
  }
  
  .charts-section {
    margin-bottom: 20px;
  }
}
</style>
