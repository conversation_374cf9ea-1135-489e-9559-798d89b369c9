{"name": "economic-calculation", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "jwt-decode": "^4.0.0", "pinia": "^3.0.3", "vue": "^3.5.12", "vue-echarts": "^7.0.3", "vue-router": "4", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "prettier": "^3.6.2", "terser": "^5.43.1", "vite": "^5.4.10"}}